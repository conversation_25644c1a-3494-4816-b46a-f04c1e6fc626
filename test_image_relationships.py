#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试图片文件名与JSON/TXT输出的对应关系
"""

import os
import json
import shutil
from docx_table_extractor import DocxTableExtractor
from simple_docx_extractor import SimpleDocxExtractor


def test_image_relationships():
    """测试图片关系映射功能"""
    docx_path = "input.docx"
    
    if not os.path.exists(docx_path):
        print(f"错误: 找不到文件 {docx_path}")
        return
    
    print("=" * 80)
    print("测试图片文件名与JSON/TXT输出的对应关系")
    print("=" * 80)
    
    # 测试简化版提取器（包含关系映射功能）
    output_dir = "test_image_relationships"
    if os.path.exists(output_dir):
        shutil.rmtree(output_dir)
    
    try:
        print("\n1. 运行简化版提取器...")
        extractor = SimpleDocxExtractor(docx_path, output_dir)
        extractor.extract_all_content()
        extractor.save_results()
        
        print("\n2. 分析图片关系映射...")
        analyze_image_relationships(output_dir)
        
        print("\n3. 生成图片关系报告...")
        generate_image_relationship_report(output_dir)
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()


def analyze_image_relationships(output_dir):
    """分析图片关系映射"""
    json_file = os.path.join(output_dir, "extraction_results.json")
    
    if not os.path.exists(json_file):
        print(f"  JSON文件不存在: {json_file}")
        return
    
    with open(json_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    # 分析图片元素
    image_elements = [elem for elem in data['elements'] if elem['element_type'] == 'image']
    
    print(f"  总图片元素数量: {len(image_elements)}")
    
    # 分类统计
    table_images = []  # 表格中的图片引用
    file_images = []   # 实际的图片文件
    
    for elem in image_elements:
        if elem['table_index'] == -1:  # 全局图片文件
            file_images.append(elem)
        else:  # 表格中的图片引用
            table_images.append(elem)
    
    print(f"  表格中的图片引用: {len(table_images)} 个")
    print(f"  实际的图片文件: {len(file_images)} 个")
    
    # 分析关系映射
    print(f"\n  图片关系映射分析:")
    
    mapped_count = 0
    unmapped_count = 0
    
    for table_img in table_images:
        embed_id = table_img.get('additional_info', {}).get('embed_id')
        actual_filename = table_img.get('additional_info', {}).get('actual_filename')
        
        if actual_filename:
            mapped_count += 1
            print(f"    ✓ 表格图片 (embed: {embed_id}) -> 文件: {actual_filename}")
        else:
            unmapped_count += 1
            print(f"    ✗ 表格图片 (embed: {embed_id}) -> 未找到对应文件")
    
    print(f"\n  映射统计:")
    print(f"    成功映射: {mapped_count} 个")
    print(f"    未映射: {unmapped_count} 个")
    
    # 分析文件引用情况
    print(f"\n  文件引用分析:")
    for file_img in file_images:
        filename = file_img['content']
        referenced_by = file_img.get('additional_info', {}).get('referenced_by_elements', [])
        
        if referenced_by:
            print(f"    文件 {filename} 被 {len(referenced_by)} 个元素引用:")
            for ref in referenced_by:
                print(f"      - 表格{ref['table_index']} 行{ref['row_index']} 列{ref['col_index']} (embed: {ref['embed_id']})")
        else:
            print(f"    文件 {filename} 未被任何元素引用")


def generate_image_relationship_report(output_dir):
    """生成图片关系报告"""
    json_file = os.path.join(output_dir, "extraction_results.json")
    report_file = os.path.join(output_dir, "image_relationship_report.txt")
    
    if not os.path.exists(json_file):
        return
    
    with open(json_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    image_elements = [elem for elem in data['elements'] if elem['element_type'] == 'image']
    
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write("图片文件名与JSON/TXT输出对应关系报告\n")
        f.write("=" * 60 + "\n\n")
        
        f.write(f"文档路径: {data['document_path']}\n")
        f.write(f"总图片元素: {len(image_elements)} 个\n\n")
        
        # 按类型分组
        table_images = [elem for elem in image_elements if elem['table_index'] != -1]
        file_images = [elem for elem in image_elements if elem['table_index'] == -1]
        
        f.write("1. 表格中的图片引用\n")
        f.write("-" * 30 + "\n")
        
        for i, elem in enumerate(table_images, 1):
            f.write(f"引用 {i}:\n")
            f.write(f"  位置: 表格{elem['table_index']} 行{elem['row_index']} 列{elem['col_index']}\n")
            f.write(f"  顺序索引: {elem['order_index']}\n")
            f.write(f"  内容描述: {elem['content']}\n")
            
            additional_info = elem.get('additional_info', {})
            embed_id = additional_info.get('embed_id')
            actual_filename = additional_info.get('actual_filename')
            content_type = additional_info.get('content_type')
            
            f.write(f"  嵌入ID: {embed_id}\n")
            if actual_filename:
                f.write(f"  对应文件: {actual_filename}\n")
                f.write(f"  文件格式: {content_type}\n")
            else:
                f.write(f"  对应文件: 未找到\n")
            f.write("\n")
        
        f.write("\n2. 实际的图片文件\n")
        f.write("-" * 30 + "\n")
        
        for i, elem in enumerate(file_images, 1):
            f.write(f"文件 {i}:\n")
            f.write(f"  文件名: {elem['content']}\n")
            f.write(f"  顺序索引: {elem['order_index']}\n")
            
            additional_info = elem.get('additional_info', {})
            f.write(f"  原始路径: {additional_info.get('original_path', 'N/A')}\n")
            f.write(f"  文件大小: {additional_info.get('file_size', 0)} bytes\n")
            f.write(f"  格式名称: {additional_info.get('format_name', 'Unknown')}\n")
            
            referenced_by = additional_info.get('referenced_by_elements', [])
            if referenced_by:
                f.write(f"  被引用次数: {len(referenced_by)}\n")
                f.write(f"  引用详情:\n")
                for ref in referenced_by:
                    f.write(f"    - 表格{ref['table_index']} 行{ref['row_index']} 列{ref['col_index']} (embed: {ref['embed_id']})\n")
            else:
                f.write(f"  被引用次数: 0\n")
            f.write("\n")
        
        f.write("\n3. 映射关系总结\n")
        f.write("-" * 30 + "\n")
        
        mapped_refs = [elem for elem in table_images 
                      if elem.get('additional_info', {}).get('actual_filename')]
        unmapped_refs = [elem for elem in table_images 
                        if not elem.get('additional_info', {}).get('actual_filename')]
        
        f.write(f"成功映射的引用: {len(mapped_refs)} 个\n")
        f.write(f"未映射的引用: {len(unmapped_refs)} 个\n")
        f.write(f"实际文件总数: {len(file_images)} 个\n")
        
        # 格式统计
        format_stats = {}
        for elem in file_images:
            format_name = elem.get('additional_info', {}).get('format_name', 'Unknown')
            format_stats[format_name] = format_stats.get(format_name, 0) + 1
        
        f.write(f"\n格式统计:\n")
        for fmt, count in sorted(format_stats.items()):
            f.write(f"  {fmt}: {count} 个\n")
    
    print(f"  图片关系报告已保存到: {report_file}")


if __name__ == "__main__":
    test_image_relationships()
    print("\n测试完成！")
