
# 使用增强版DocX提取器的示例代码

from enhanced_docx_extractor import EnhancedDocxExtractor

# 1. 创建提取器实例
extractor = EnhancedDocxExtractor("input.docx", "output_directory")

# 2. 执行完整提取（包括EMF/WMF支持和图片关系映射）
extractor.extract_all_content()

# 3. 保存结果
extractor.save_results()

# 输出文件说明：
# - extraction_results.json: 完整的JSON格式结果
# - extraction_report.txt: 可读的文本报告
# - image_relationship_report.txt: 图片文件名与JSON/TXT对应关系报告
# - images/: 提取的所有图片文件（包括EMF/WMF）

# 主要特性：
# ✅ 支持EMF和WMF格式图片提取
# ✅ 完整的图片关系映射（embed_id ↔ 实际文件名）
# ✅ 文件头验证确保格式正确性
# ✅ 支持多种EMF/WMF content_type
# ✅ 详细的提取报告和统计信息
