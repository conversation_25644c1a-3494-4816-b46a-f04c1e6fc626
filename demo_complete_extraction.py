#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整的DocX提取演示脚本
展示EMF/WMF支持和图片文件名与JSON/TXT输出的对应关系
"""

import os
import json
from enhanced_docx_extractor import EnhancedDocxExtractor


def demo_complete_extraction(docx_path: str):
    """演示完整的提取功能"""
    print("🚀 DocX完整提取演示")
    print("=" * 60)
    print(f"📄 文档: {docx_path}")
    
    if not os.path.exists(docx_path):
        print(f"❌ 错误: 找不到文件 {docx_path}")
        return
    
    output_dir = "complete_extraction_demo"
    
    try:
        # 创建增强版提取器
        print("\n🔧 初始化增强版提取器...")
        extractor = EnhancedDocxExtractor(docx_path, output_dir)
        
        # 执行完整提取
        print("\n📊 开始完整提取...")
        extractor.extract_all_content()
        
        # 保存结果
        print("\n💾 保存提取结果...")
        extractor.save_results()
        
        # 分析和展示结果
        print("\n📋 分析提取结果...")
        analyze_extraction_results(output_dir)
        
        print(f"\n✅ 演示完成!")
        print(f"📁 结果目录: {output_dir}")
        
    except Exception as e:
        print(f"❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


def analyze_extraction_results(output_dir: str):
    """分析提取结果"""
    json_path = os.path.join(output_dir, "extraction_results.json")
    
    if not os.path.exists(json_path):
        print("❌ 未找到提取结果文件")
        return
    
    try:
        with open(json_path, 'r', encoding='utf-8') as f:
            results = json.load(f)
        
        print(f"📊 提取统计:")
        print(f"  总元素数: {results['total_elements']}")
        
        # 元素类型统计
        summary = results.get('extraction_summary', {})
        for element_type, count in summary.items():
            print(f"  {element_type}: {count} 个")
        
        # 图片关系映射分析
        print(f"\n🔗 图片关系映射:")
        mapping = results.get('image_relationship_mapping', {})
        print(f"  关系映射数: {len(mapping)}")
        
        # 分析图片格式
        format_stats = {}
        emf_wmf_count = 0
        
        for rel_id, rel_info in mapping.items():
            format_name = rel_info.get('format_name', 'Unknown')
            format_stats[format_name] = format_stats.get(format_name, 0) + 1
            
            if format_name in ['EMF', 'WMF']:
                emf_wmf_count += 1
        
        if format_stats:
            print(f"  格式分布:")
            for fmt, count in sorted(format_stats.items()):
                print(f"    {fmt}: {count} 个")
        
        print(f"  EMF/WMF数量: {emf_wmf_count}")
        
        # 分析图片元素
        print(f"\n🖼️ 图片元素分析:")
        image_elements = [e for e in results['elements'] if e['element_type'] == 'image']
        table_refs = [e for e in image_elements if e['table_index'] != -1]
        actual_files = [e for e in image_elements if e['table_index'] == -1]
        
        print(f"  图片引用数: {len(table_refs)}")
        print(f"  实际文件数: {len(actual_files)}")
        
        # 映射成功率
        mapped_refs = 0
        for elem in table_refs:
            additional_info = elem.get('additional_info', {})
            if additional_info.get('actual_filename'):
                mapped_refs += 1
        
        if table_refs:
            success_rate = mapped_refs / len(table_refs) * 100
            print(f"  映射成功率: {success_rate:.1f}%")
        
        # 展示具体的映射关系
        print(f"\n📝 图片文件名与输出对应关系:")
        show_image_mapping_details(table_refs, actual_files)
        
        # EMF/WMF特别分析
        show_emf_wmf_analysis(actual_files)
        
    except Exception as e:
        print(f"❌ 分析结果时出错: {e}")


def show_image_mapping_details(table_refs: list, actual_files: list):
    """展示图片映射详情"""
    print("  表格引用 → 实际文件:")
    
    if not table_refs:
        print("    (无图片引用)")
        return
    
    for i, ref_elem in enumerate(table_refs, 1):
        additional_info = ref_elem.get('additional_info', {})
        embed_id = additional_info.get('embed_id', 'N/A')
        actual_filename = additional_info.get('actual_filename', '未映射')
        content_type = additional_info.get('content_type', 'N/A')
        
        position = f"表格{ref_elem['table_index']}行{ref_elem['row_index']}列{ref_elem['col_index']}"
        
        print(f"    {i:2d}. {position}")
        print(f"        嵌入ID: {embed_id}")
        print(f"        对应文件: {actual_filename}")
        print(f"        格式类型: {content_type}")
        print()


def show_emf_wmf_analysis(actual_files: list):
    """展示EMF/WMF分析"""
    emf_wmf_files = []
    
    for elem in actual_files:
        additional_info = elem.get('additional_info', {})
        format_name = additional_info.get('format_name', 'Unknown')
        
        if format_name in ['EMF', 'WMF']:
            emf_wmf_files.append({
                'filename': elem['content'],
                'format': format_name,
                'size': additional_info.get('file_size', 0),
                'is_valid': additional_info.get('is_valid_format', False),
                'referenced_by': len(additional_info.get('referenced_by_elements', []))
            })
    
    if emf_wmf_files:
        print(f"\n🎯 EMF/WMF文件详细信息 ({len(emf_wmf_files)} 个):")
        
        for i, file_info in enumerate(emf_wmf_files, 1):
            status = "✅" if file_info['is_valid'] else "⚠️"
            print(f"  {i}. {file_info['filename']}")
            print(f"     格式: {file_info['format']}")
            print(f"     大小: {file_info['size']:,} bytes")
            print(f"     验证: {status} {'有效' if file_info['is_valid'] else '无效'}")
            print(f"     引用次数: {file_info['referenced_by']}")
            print()
    else:
        print(f"\nℹ️ 文档中没有EMF/WMF格式的图片")


def show_file_structure(output_dir: str):
    """展示输出文件结构"""
    print(f"\n📁 输出文件结构:")
    
    if not os.path.exists(output_dir):
        print("  (输出目录不存在)")
        return
    
    # 主要文件
    main_files = [
        "extraction_results.json",
        "extraction_report.txt", 
        "image_relationship_report.txt"
    ]
    
    for filename in main_files:
        filepath = os.path.join(output_dir, filename)
        if os.path.exists(filepath):
            size = os.path.getsize(filepath)
            print(f"  📄 {filename} ({size:,} bytes)")
        else:
            print(f"  ❌ {filename} (不存在)")
    
    # 图片目录
    images_dir = os.path.join(output_dir, "images")
    if os.path.exists(images_dir):
        image_files = [f for f in os.listdir(images_dir) 
                      if os.path.isfile(os.path.join(images_dir, f))]
        print(f"  📁 images/ ({len(image_files)} 个文件)")
        
        # 按格式分组
        format_groups = {}
        for img_file in image_files:
            ext = os.path.splitext(img_file)[1].lower()
            format_groups[ext] = format_groups.get(ext, 0) + 1
        
        for ext, count in sorted(format_groups.items()):
            print(f"    {ext}: {count} 个")
    else:
        print(f"  ❌ images/ (不存在)")


def create_usage_example():
    """创建使用示例"""
    example_code = '''
# 使用增强版DocX提取器的示例代码

from enhanced_docx_extractor import EnhancedDocxExtractor

# 1. 创建提取器实例
extractor = EnhancedDocxExtractor("input.docx", "output_directory")

# 2. 执行完整提取（包括EMF/WMF支持和图片关系映射）
extractor.extract_all_content()

# 3. 保存结果
extractor.save_results()

# 输出文件说明：
# - extraction_results.json: 完整的JSON格式结果
# - extraction_report.txt: 可读的文本报告
# - image_relationship_report.txt: 图片文件名与JSON/TXT对应关系报告
# - images/: 提取的所有图片文件（包括EMF/WMF）

# 主要特性：
# ✅ 支持EMF和WMF格式图片提取
# ✅ 完整的图片关系映射（embed_id ↔ 实际文件名）
# ✅ 文件头验证确保格式正确性
# ✅ 支持多种EMF/WMF content_type
# ✅ 详细的提取报告和统计信息
'''
    
    with open("usage_example.py", 'w', encoding='utf-8') as f:
        f.write(example_code)
    
    print(f"\n📝 使用示例已保存到: usage_example.py")


def main():
    """主函数"""
    docx_path = "input.docx"
    
    print("🎯 DocX完整提取功能演示")
    print("包含EMF/WMF支持和图片文件名对应关系")
    print("=" * 60)
    
    # 检查输入文件
    if not os.path.exists(docx_path):
        print(f"❌ 找不到输入文件: {docx_path}")
        print("请确保当前目录下有 input.docx 文件")
        return
    
    try:
        # 执行完整演示
        demo_complete_extraction(docx_path)
        
        # 展示文件结构
        show_file_structure("complete_extraction_demo")
        
        # 创建使用示例
        create_usage_example()
        
        print(f"\n🎉 完整演示结束!")
        print(f"📋 主要功能:")
        print(f"  ✅ EMF/WMF格式图片提取")
        print(f"  ✅ 图片文件名与JSON/TXT对应关系")
        print(f"  ✅ 完整的关系映射和验证")
        print(f"  ✅ 详细的分析报告")
        
        print(f"\n📁 查看结果:")
        print(f"  📄 complete_extraction_demo/extraction_results.json")
        print(f"  📄 complete_extraction_demo/image_relationship_report.txt")
        print(f"  📁 complete_extraction_demo/images/")
        
    except Exception as e:
        print(f"❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
