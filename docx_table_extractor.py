#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DocX表格内容提取器
提取docx文档中表格单元格的所有内容（文本、图片、文本框、嵌套表格）并建立顺序关系
"""

import os
import json
from typing import List, Dict, Any, Tuple
from dataclasses import dataclass, asdict
from docx import Document
from docx.document import Document as DocumentType
from docx.table import Table, _Cell
from docx.text.paragraph import Paragraph
from docx.oxml.ns import qn
from docx.oxml import parse_xml
import zipfile
import shutil


@dataclass
class ExtractedElement:
    """提取的元素数据结构"""
    element_type: str  # 'text', 'image', 'textbox', 'table'
    content: str
    table_index: int
    row_index: int
    col_index: int
    order_index: int
    additional_info: Dict[str, Any] = None


class DocxTableExtractor:
    """DocX表格内容提取器"""
    
    def __init__(self, docx_path: str, output_dir: str = "extracted_content"):
        """
        初始化提取器
        
        Args:
            docx_path: docx文档路径
            output_dir: 输出目录
        """
        self.docx_path = docx_path
        self.output_dir = output_dir
        self.document = None
        self.extracted_elements = []
        self.order_counter = 0
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        os.makedirs(os.path.join(output_dir, "images"), exist_ok=True)
        
    def load_document(self):
        """加载docx文档"""
        try:
            self.document = Document(self.docx_path)
            print(f"成功加载文档: {self.docx_path}")
        except Exception as e:
            print(f"加载文档失败: {e}")
            raise
    
    def extract_all_content(self):
        """提取所有表格内容"""
        if not self.document:
            self.load_document()
        
        print(f"开始提取文档中的表格内容...")
        print(f"文档中共有 {len(self.document.tables)} 个表格")
        
        for table_idx, table in enumerate(self.document.tables):
            print(f"\n处理第 {table_idx + 1} 个表格...")
            self._extract_table_content(table, table_idx)
        
        print(f"\n提取完成！共提取 {len(self.extracted_elements)} 个元素")
        
    def _extract_table_content(self, table: Table, table_index: int):
        """提取单个表格的内容"""
        for row_idx, row in enumerate(table.rows):
            for col_idx, cell in enumerate(row.cells):
                print(f"  处理单元格 [{row_idx}, {col_idx}]...")
                self._extract_cell_content(cell, table_index, row_idx, col_idx)
    
    def _extract_cell_content(self, cell: _Cell, table_index: int, row_index: int, col_index: int):
        """提取单元格内容"""
        # 提取文本内容
        self._extract_text_from_cell(cell, table_index, row_index, col_index)
        
        # 提取图片
        self._extract_images_from_cell(cell, table_index, row_index, col_index)
        
        # 提取文本框
        self._extract_textboxes_from_cell(cell, table_index, row_index, col_index)
        
        # 提取嵌套表格
        self._extract_nested_tables_from_cell(cell, table_index, row_index, col_index)
    
    def _extract_text_from_cell(self, cell: _Cell, table_index: int, row_index: int, col_index: int):
        """提取单元格中的文本内容"""
        for paragraph in cell.paragraphs:
            text_content = paragraph.text.strip()
            if text_content:
                element = ExtractedElement(
                    element_type="text",
                    content=text_content,
                    table_index=table_index,
                    row_index=row_index,
                    col_index=col_index,
                    order_index=self.order_counter,
                    additional_info={
                        "paragraph_style": paragraph.style.name if paragraph.style else None,
                        "runs_count": len(paragraph.runs)
                    }
                )
                self.extracted_elements.append(element)
                self.order_counter += 1
    
    def _extract_images_from_cell(self, cell: _Cell, table_index: int, row_index: int, col_index: int):
        """提取单元格中的图片"""
        try:
            # 通过XML解析查找图片
            cell_xml = cell._tc
            
            # 查找所有的图片引用
            drawing_elements = cell_xml.xpath('.//w:drawing', namespaces={
                'w': 'http://schemas.openxmlformats.org/wordprocessingml/2006/main'
            })
            
            for drawing in drawing_elements:
                # 查找图片的关系ID
                blip_elements = drawing.xpath('.//a:blip/@r:embed', namespaces={
                    'a': 'http://schemas.openxmlformats.org/drawingml/2006/main',
                    'r': 'http://schemas.openxmlformats.org/officeDocument/2006/relationships'
                })
                
                for blip_id in blip_elements:
                    image_info = self._extract_image_by_rel_id(blip_id, table_index, row_index, col_index)
                    if image_info:
                        element = ExtractedElement(
                            element_type="image",
                            content=image_info["filename"],
                            table_index=table_index,
                            row_index=row_index,
                            col_index=col_index,
                            order_index=self.order_counter,
                            additional_info=image_info
                        )
                        self.extracted_elements.append(element)
                        self.order_counter += 1
                        
        except Exception as e:
            print(f"    提取图片时出错: {e}")
    
    def _extract_image_by_rel_id(self, rel_id: str, table_index: int, row_index: int, col_index: int) -> Dict[str, Any]:
        """通过关系ID提取图片"""
        try:
            # 获取文档的关系
            part = self.document.part
            if rel_id in part.rels:
                image_part = part.rels[rel_id].target_part
                
                # 生成图片文件名
                image_ext = image_part.content_type.split('/')[-1]
                if image_ext == 'jpeg':
                    image_ext = 'jpg'
                
                filename = f"table_{table_index}_row_{row_index}_col_{col_index}_img_{self.order_counter}.{image_ext}"
                filepath = os.path.join(self.output_dir, "images", filename)
                
                # 保存图片
                with open(filepath, 'wb') as f:
                    f.write(image_part.blob)
                
                return {
                    "filename": filename,
                    "filepath": filepath,
                    "content_type": image_part.content_type,
                    "size": len(image_part.blob)
                }
        except Exception as e:
            print(f"    保存图片失败: {e}")
        
        return None
    
    def _extract_textboxes_from_cell(self, cell: _Cell, table_index: int, row_index: int, col_index: int):
        """提取单元格中的文本框"""
        try:
            cell_xml = cell._tc
            
            # 查找文本框元素
            textbox_elements = cell_xml.xpath('.//w:txbxContent', namespaces={
                'w': 'http://schemas.openxmlformats.org/wordprocessingml/2006/main'
            })
            
            for textbox in textbox_elements:
                # 提取文本框中的文本
                textbox_text = ""
                p_elements = textbox.xpath('.//w:p', namespaces={
                    'w': 'http://schemas.openxmlformats.org/wordprocessingml/2006/main'
                })
                
                for p in p_elements:
                    t_elements = p.xpath('.//w:t', namespaces={
                        'w': 'http://schemas.openxmlformats.org/wordprocessingml/2006/main'
                    })
                    for t in t_elements:
                        if t.text:
                            textbox_text += t.text
                    textbox_text += "\n"
                
                if textbox_text.strip():
                    element = ExtractedElement(
                        element_type="textbox",
                        content=textbox_text.strip(),
                        table_index=table_index,
                        row_index=row_index,
                        col_index=col_index,
                        order_index=self.order_counter,
                        additional_info={"paragraphs_count": len(p_elements)}
                    )
                    self.extracted_elements.append(element)
                    self.order_counter += 1
                    
        except Exception as e:
            print(f"    提取文本框时出错: {e}")
    
    def _extract_nested_tables_from_cell(self, cell: _Cell, table_index: int, row_index: int, col_index: int):
        """提取单元格中的嵌套表格"""
        try:
            cell_xml = cell._tc
            
            # 查找嵌套的表格元素
            nested_table_elements = cell_xml.xpath('.//w:tbl', namespaces={
                'w': 'http://schemas.openxmlformats.org/wordprocessingml/2006/main'
            })
            
            for nested_table_xml in nested_table_elements:
                # 提取嵌套表格的基本信息
                rows = nested_table_xml.xpath('.//w:tr', namespaces={
                    'w': 'http://schemas.openxmlformats.org/wordprocessingml/2006/main'
                })
                
                nested_table_info = {
                    "rows_count": len(rows),
                    "cells_data": []
                }
                
                # 提取嵌套表格的内容
                for nested_row_idx, row in enumerate(rows):
                    cells = row.xpath('.//w:tc', namespaces={
                        'w': 'http://schemas.openxmlformats.org/wordprocessingml/2006/main'
                    })
                    
                    row_data = []
                    for nested_col_idx, nested_cell in enumerate(cells):
                        # 提取嵌套单元格的文本
                        cell_text = ""
                        t_elements = nested_cell.xpath('.//w:t', namespaces={
                            'w': 'http://schemas.openxmlformats.org/wordprocessingml/2006/main'
                        })
                        for t in t_elements:
                            if t.text:
                                cell_text += t.text
                        
                        row_data.append({
                            "row": nested_row_idx,
                            "col": nested_col_idx,
                            "text": cell_text.strip()
                        })
                    
                    nested_table_info["cells_data"].extend(row_data)
                
                element = ExtractedElement(
                    element_type="nested_table",
                    content=f"嵌套表格 ({len(rows)} 行)",
                    table_index=table_index,
                    row_index=row_index,
                    col_index=col_index,
                    order_index=self.order_counter,
                    additional_info=nested_table_info
                )
                self.extracted_elements.append(element)
                self.order_counter += 1
                
        except Exception as e:
            print(f"    提取嵌套表格时出错: {e}")
    
    def save_results(self):
        """保存提取结果"""
        # 保存为JSON格式
        results = {
            "document_path": self.docx_path,
            "total_elements": len(self.extracted_elements),
            "extraction_summary": self._get_extraction_summary(),
            "elements": [asdict(element) for element in self.extracted_elements]
        }
        
        json_path = os.path.join(self.output_dir, "extraction_results.json")
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        # 生成可读的报告
        self._generate_readable_report()
        
        print(f"\n结果已保存到: {self.output_dir}")
        print(f"- JSON结果: {json_path}")
        print(f"- 可读报告: {os.path.join(self.output_dir, 'extraction_report.txt')}")
        print(f"- 图片目录: {os.path.join(self.output_dir, 'images')}")
    
    def _get_extraction_summary(self) -> Dict[str, int]:
        """获取提取摘要"""
        summary = {}
        for element in self.extracted_elements:
            element_type = element.element_type
            summary[element_type] = summary.get(element_type, 0) + 1
        return summary
    
    def _generate_readable_report(self):
        """生成可读的提取报告"""
        report_path = os.path.join(self.output_dir, "extraction_report.txt")
        
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("DocX表格内容提取报告\n")
            f.write("=" * 50 + "\n\n")
            
            f.write(f"文档路径: {self.docx_path}\n")
            f.write(f"提取时间: {self._get_current_time()}\n")
            f.write(f"总元素数: {len(self.extracted_elements)}\n\n")
            
            # 提取摘要
            summary = self._get_extraction_summary()
            f.write("提取摘要:\n")
            for element_type, count in summary.items():
                f.write(f"  {element_type}: {count} 个\n")
            f.write("\n")
            
            # 按顺序列出所有元素
            f.write("详细内容 (按提取顺序):\n")
            f.write("-" * 30 + "\n")
            
            for element in self.extracted_elements:
                f.write(f"\n[{element.order_index}] {element.element_type.upper()}\n")
                f.write(f"位置: 表格{element.table_index} -> 行{element.row_index} -> 列{element.col_index}\n")
                f.write(f"内容: {element.content[:100]}{'...' if len(element.content) > 100 else ''}\n")
                
                if element.additional_info:
                    f.write(f"附加信息: {element.additional_info}\n")
    
    def _get_current_time(self) -> str:
        """获取当前时间字符串"""
        from datetime import datetime
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")


def main():
    """主函数"""
    # 使用示例
    docx_path = "input.docx"  # 输入的docx文件路径
    output_dir = "extracted_content"  # 输出目录
    
    if not os.path.exists(docx_path):
        print(f"错误: 找不到文件 {docx_path}")
        return
    
    try:
        # 创建提取器
        extractor = DocxTableExtractor(docx_path, output_dir)
        
        # 提取内容
        extractor.extract_all_content()
        
        # 保存结果
        extractor.save_results()
        
        print("\n提取完成！")
        
    except Exception as e:
        print(f"提取过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
