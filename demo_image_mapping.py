#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
演示图片文件名与JSON/TXT输出对应关系的完整功能
"""

import os
import json
import shutil
from simple_docx_extractor import SimpleDocxExtractor


def demo_image_mapping():
    """演示图片关系映射功能"""
    print("=" * 80)
    print("DocX图片提取与关系映射演示")
    print("=" * 80)
    
    docx_path = "input.docx"
    output_dir = "demo_output"
    
    if not os.path.exists(docx_path):
        print(f"错误: 找不到文件 {docx_path}")
        return
    
    # 清理输出目录
    if os.path.exists(output_dir):
        shutil.rmtree(output_dir)
    
    print(f"\n📄 处理文档: {docx_path}")
    print(f"📁 输出目录: {output_dir}")
    
    try:
        # 1. 运行提取器
        print(f"\n🔄 步骤1: 运行图片提取器...")
        extractor = SimpleDocxExtractor(docx_path, output_dir)
        extractor.extract_all_content()
        extractor.save_results()
        
        # 2. 分析结果
        print(f"\n📊 步骤2: 分析提取结果...")
        analyze_results(output_dir)
        
        # 3. 展示关系映射
        print(f"\n🔗 步骤3: 展示图片关系映射...")
        show_image_mapping(output_dir)
        
        # 4. 生成使用示例
        print(f"\n💡 步骤4: 生成使用示例...")
        generate_usage_examples(output_dir)
        
        print(f"\n✅ 演示完成！")
        print(f"📋 详细报告: {output_dir}/image_relationship_report.txt")
        print(f"📄 JSON结果: {output_dir}/extraction_results.json")
        print(f"🖼️  图片文件: {output_dir}/images/")
        
    except Exception as e:
        print(f"❌ 演示失败: {e}")
        import traceback
        traceback.print_exc()


def analyze_results(output_dir):
    """分析提取结果"""
    json_file = os.path.join(output_dir, "extraction_results.json")
    
    with open(json_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    total_elements = data['total_elements']
    summary = data['extraction_summary']
    
    print(f"  📈 总提取元素: {total_elements} 个")
    print(f"  📝 文本元素: {summary.get('text', 0)} 个")
    print(f"  🖼️  图片元素: {summary.get('image', 0)} 个")
    print(f"  📋 嵌套表格: {summary.get('nested_table', 0)} 个")
    
    # 统计图片文件
    images_dir = os.path.join(output_dir, "images")
    if os.path.exists(images_dir):
        image_files = [f for f in os.listdir(images_dir) if os.path.isfile(os.path.join(images_dir, f))]
        print(f"  💾 实际图片文件: {len(image_files)} 个")
        
        # 按格式统计
        format_stats = {}
        for filename in image_files:
            ext = os.path.splitext(filename)[1].lower()
            format_stats[ext] = format_stats.get(ext, 0) + 1
        
        print(f"  📊 格式分布:")
        for ext, count in sorted(format_stats.items()):
            if ext:
                print(f"    {ext.upper()}: {count} 个")
            else:
                print(f"    无扩展名: {count} 个")


def show_image_mapping(output_dir):
    """展示图片关系映射"""
    json_file = os.path.join(output_dir, "extraction_results.json")
    
    with open(json_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    image_elements = [elem for elem in data['elements'] if elem['element_type'] == 'image']
    
    # 分类
    table_refs = [elem for elem in image_elements if elem['table_index'] != -1]
    actual_files = [elem for elem in image_elements if elem['table_index'] == -1]
    
    print(f"  🔍 表格中的图片引用: {len(table_refs)} 个")
    print(f"  💾 实际的图片文件: {len(actual_files)} 个")
    
    # 展示成功映射的关系
    print(f"\n  ✅ 成功建立的映射关系:")
    mapped_count = 0
    
    for ref in table_refs:
        embed_id = ref.get('additional_info', {}).get('embed_id')
        actual_filename = ref.get('additional_info', {}).get('actual_filename')
        
        if actual_filename:
            mapped_count += 1
            table_pos = f"表格{ref['table_index']} 行{ref['row_index']} 列{ref['col_index']}"
            print(f"    {mapped_count}. {table_pos} (embed: {embed_id}) → {actual_filename}")
    
    print(f"\n  📊 映射统计:")
    print(f"    成功映射: {mapped_count} 个")
    print(f"    映射成功率: {mapped_count/len(table_refs)*100:.1f}%" if table_refs else "    映射成功率: N/A")
    
    # 展示未被引用的文件
    unreferenced_files = []
    for file_elem in actual_files:
        referenced_by = file_elem.get('additional_info', {}).get('referenced_by_elements', [])
        if not referenced_by:
            unreferenced_files.append(file_elem['content'])
    
    if unreferenced_files:
        print(f"\n  ⚠️  未被引用的文件 ({len(unreferenced_files)} 个):")
        for filename in unreferenced_files:
            print(f"    - {filename}")


def generate_usage_examples(output_dir):
    """生成使用示例"""
    json_file = os.path.join(output_dir, "extraction_results.json")
    examples_file = os.path.join(output_dir, "usage_examples.py")
    
    with open(json_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    image_elements = [elem for elem in data['elements'] if elem['element_type'] == 'image']
    table_refs = [elem for elem in image_elements if elem['table_index'] != -1]
    
    with open(examples_file, 'w', encoding='utf-8') as f:
        f.write('#!/usr/bin/env python3\n')
        f.write('# -*- coding: utf-8 -*-\n')
        f.write('"""\n')
        f.write('图片关系映射使用示例\n')
        f.write('"""\n\n')
        f.write('import json\n')
        f.write('import os\n\n')
        
        f.write('def load_extraction_results(json_file):\n')
        f.write('    """加载提取结果"""\n')
        f.write('    with open(json_file, \'r\', encoding=\'utf-8\') as f:\n')
        f.write('        return json.load(f)\n\n')
        
        f.write('def find_image_by_position(data, table_index, row_index, col_index):\n')
        f.write('    """根据位置查找图片"""\n')
        f.write('    for elem in data[\'elements\']:\n')
        f.write('        if (elem[\'element_type\'] == \'image\' and\n')
        f.write('            elem[\'table_index\'] == table_index and\n')
        f.write('            elem[\'row_index\'] == row_index and\n')
        f.write('            elem[\'col_index\'] == col_index):\n')
        f.write('            return elem\n')
        f.write('    return None\n\n')
        
        f.write('def get_actual_filename(image_element):\n')
        f.write('    """获取图片元素对应的实际文件名"""\n')
        f.write('    if image_element and \'additional_info\' in image_element:\n')
        f.write('        return image_element[\'additional_info\'].get(\'actual_filename\')\n')
        f.write('    return None\n\n')
        
        f.write('# 使用示例\n')
        f.write('if __name__ == "__main__":\n')
        f.write('    # 加载数据\n')
        f.write('    data = load_extraction_results("extraction_results.json")\n\n')
        
        # 生成具体的使用示例
        if table_refs:
            example_ref = table_refs[0]
            f.write('    # 示例1: 查找特定位置的图片\n')
            f.write(f'    image = find_image_by_position(data, {example_ref["table_index"]}, {example_ref["row_index"]}, {example_ref["col_index"]})\n')
            f.write('    if image:\n')
            f.write('        filename = get_actual_filename(image)\n')
            f.write('        print(f"找到图片: {filename}")\n')
            f.write('        print(f"嵌入ID: {image[\'additional_info\'].get(\'embed_id\')}")\n\n')
        
        f.write('    # 示例2: 遍历所有图片引用\n')
        f.write('    print("所有图片引用:")\n')
        f.write('    for elem in data[\'elements\']:\n')
        f.write('        if elem[\'element_type\'] == \'image\' and elem[\'table_index\'] != -1:\n')
        f.write('            filename = get_actual_filename(elem)\n')
        f.write('            pos = f"表格{elem[\'table_index\']} 行{elem[\'row_index\']} 列{elem[\'col_index\']}"\n')
        f.write('            embed_id = elem[\'additional_info\'].get(\'embed_id\')\n')
        f.write('            print(f"  {pos} (embed: {embed_id}) -> {filename}")\n')
    
    print(f"  📝 使用示例已生成: {examples_file}")


if __name__ == "__main__":
    demo_image_mapping()
