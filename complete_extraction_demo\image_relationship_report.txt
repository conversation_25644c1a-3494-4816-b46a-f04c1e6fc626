图片文件名与JSON/TXT输出对应关系报告
============================================================

文档路径: input.docx
总图片元素: 21 个

1. 表格中的图片引用
------------------------------
引用 1:
  位置: 表格5 行1 列1
  顺序索引: 118
  内容描述: 图片_表格5_行1_列1_序号118
  嵌入ID: rId17
  对应文件: extracted_image_10.png
  文件格式: image/png

引用 2:
  位置: 表格5 行1 列1
  顺序索引: 119
  内容描述: 图片_表格5_行1_列1_序号119
  嵌入ID: rId18
  对应文件: extracted_image_11.png
  文件格式: image/png

引用 3:
  位置: 表格5 行1 列1
  顺序索引: 120
  内容描述: 图片_表格5_行1_列1_序号120
  嵌入ID: rId19
  对应文件: extracted_image_12.png
  文件格式: image/png

引用 4:
  位置: 表格5 行1 列1
  顺序索引: 121
  内容描述: 图片_表格5_行1_列1_序号121
  嵌入ID: rId20
  对应文件: extracted_image_13.png
  文件格式: image/png

引用 5:
  位置: 表格5 行1 列1
  顺序索引: 122
  内容描述: 图片_表格5_行1_列1_序号122
  嵌入ID: rId21
  对应文件: extracted_image_14.png
  文件格式: image/png

引用 6:
  位置: 表格5 行1 列1
  顺序索引: 123
  内容描述: 图片_表格5_行1_列1_序号123
  嵌入ID: rId22
  对应文件: extracted_image_3.png
  文件格式: image/png

引用 7:
  位置: 表格5 行1 列1
  顺序索引: 124
  内容描述: 图片_表格5_行1_列1_序号124
  嵌入ID: rId23
  对应文件: extracted_image_4.png
  文件格式: image/png


2. 实际的图片文件
------------------------------
文件 1:
  文件名: extracted_image_1
  顺序索引: 503
  原始路径: word/media/
  文件大小: 0 bytes
  格式名称: Unknown
  格式验证: 失败
  被引用次数: 0

文件 2:
  文件名: extracted_image_2.emf
  顺序索引: 504
  原始路径: word/media/image1.emf
  文件大小: 28488 bytes
  格式名称: EMF
  格式验证: 通过
  被引用次数: 0

文件 3:
  文件名: extracted_image_3.png
  顺序索引: 505
  原始路径: word/media/image10.png
  文件大小: 25313 bytes
  格式名称: PNG
  格式验证: 通过
  被引用次数: 1
  引用详情:
    - 表格5 行1 列1 (embed: rId22)

文件 4:
  文件名: extracted_image_4.png
  顺序索引: 506
  原始路径: word/media/image11.png
  文件大小: 21439 bytes
  格式名称: PNG
  格式验证: 通过
  被引用次数: 1
  引用详情:
    - 表格5 行1 列1 (embed: rId23)

文件 5:
  文件名: extracted_image_5.wmf
  顺序索引: 507
  原始路径: word/media/image12.wmf
  文件大小: 1466 bytes
  格式名称: WMF
  格式验证: 通过
  被引用次数: 0

文件 6:
  文件名: extracted_image_6.wmf
  顺序索引: 508
  原始路径: word/media/image13.wmf
  文件大小: 924 bytes
  格式名称: WMF
  格式验证: 通过
  被引用次数: 0

文件 7:
  文件名: extracted_image_7.emf
  顺序索引: 509
  原始路径: word/media/image2.emf
  文件大小: 33212 bytes
  格式名称: EMF
  格式验证: 通过
  被引用次数: 0

文件 8:
  文件名: extracted_image_8.emf
  顺序索引: 510
  原始路径: word/media/image3.emf
  文件大小: 40096 bytes
  格式名称: EMF
  格式验证: 通过
  被引用次数: 0

文件 9:
  文件名: extracted_image_9.emf
  顺序索引: 511
  原始路径: word/media/image4.emf
  文件大小: 22852 bytes
  格式名称: EMF
  格式验证: 通过
  被引用次数: 0

文件 10:
  文件名: extracted_image_10.png
  顺序索引: 512
  原始路径: word/media/image5.png
  文件大小: 117167 bytes
  格式名称: PNG
  格式验证: 通过
  被引用次数: 1
  引用详情:
    - 表格5 行1 列1 (embed: rId17)

文件 11:
  文件名: extracted_image_11.png
  顺序索引: 513
  原始路径: word/media/image6.png
  文件大小: 18351 bytes
  格式名称: PNG
  格式验证: 通过
  被引用次数: 1
  引用详情:
    - 表格5 行1 列1 (embed: rId18)

文件 12:
  文件名: extracted_image_12.png
  顺序索引: 514
  原始路径: word/media/image7.png
  文件大小: 17857 bytes
  格式名称: PNG
  格式验证: 通过
  被引用次数: 1
  引用详情:
    - 表格5 行1 列1 (embed: rId19)

文件 13:
  文件名: extracted_image_13.png
  顺序索引: 515
  原始路径: word/media/image8.png
  文件大小: 16581 bytes
  格式名称: PNG
  格式验证: 通过
  被引用次数: 1
  引用详情:
    - 表格5 行1 列1 (embed: rId20)

文件 14:
  文件名: extracted_image_14.png
  顺序索引: 516
  原始路径: word/media/image9.png
  文件大小: 14310 bytes
  格式名称: PNG
  格式验证: 通过
  被引用次数: 1
  引用详情:
    - 表格5 行1 列1 (embed: rId21)


3. 映射关系总结
------------------------------
成功映射的引用: 7 个
未映射的引用: 0 个
实际文件总数: 14 个
映射成功率: 100.0%
