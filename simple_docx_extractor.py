#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版DocX表格内容提取器
专注于提取基本内容，避免复杂的XML操作
"""

import os
import json
from typing import List, Dict, Any
from dataclasses import dataclass, asdict
from docx import Document
from docx.table import Table, _Cell
import zipfile
import shutil


@dataclass
class ExtractedElement:
    """提取的元素数据结构"""
    element_type: str  # 'text', 'image', 'textbox', 'table'
    content: str
    table_index: int
    row_index: int
    col_index: int
    order_index: int
    additional_info: Dict[str, Any] = None


class SimpleDocxExtractor:
    """简化版DocX表格内容提取器"""
    
    def __init__(self, docx_path: str, output_dir: str = "extracted_content"):
        """
        初始化提取器
        
        Args:
            docx_path: docx文档路径
            output_dir: 输出目录
        """
        self.docx_path = docx_path
        self.output_dir = output_dir
        self.document = None
        self.extracted_elements = []
        self.order_counter = 0
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        os.makedirs(os.path.join(output_dir, "images"), exist_ok=True)
        
    def load_document(self):
        """加载docx文档"""
        try:
            self.document = Document(self.docx_path)
            print(f"成功加载文档: {self.docx_path}")
        except Exception as e:
            print(f"加载文档失败: {e}")
            raise
    
    def extract_all_content(self):
        """提取所有表格内容"""
        if not self.document:
            self.load_document()
        
        print(f"开始提取文档中的表格内容...")
        print(f"文档中共有 {len(self.document.tables)} 个表格")
        
        for table_idx, table in enumerate(self.document.tables):
            print(f"\n处理第 {table_idx + 1} 个表格...")
            self._extract_table_content(table, table_idx)
        
        print(f"\n提取完成！共提取 {len(self.extracted_elements)} 个元素")
        
    def _extract_table_content(self, table: Table, table_index: int):
        """提取单个表格的内容"""
        for row_idx, row in enumerate(table.rows):
            for col_idx, cell in enumerate(row.cells):
                print(f"  处理单元格 [{row_idx}, {col_idx}]...")
                self._extract_cell_content(cell, table_index, row_idx, col_idx)
    
    def _extract_cell_content(self, cell: _Cell, table_index: int, row_index: int, col_index: int):
        """提取单元格内容"""
        # 提取文本内容
        self._extract_text_from_cell(cell, table_index, row_index, col_index)
        
        # 提取图片（简化版本）
        self._extract_images_from_cell_simple(cell, table_index, row_index, col_index)
        
        # 检查是否有嵌套表格
        self._check_nested_tables(cell, table_index, row_index, col_index)
    
    def _extract_text_from_cell(self, cell: _Cell, table_index: int, row_index: int, col_index: int):
        """提取单元格中的文本内容"""
        for paragraph in cell.paragraphs:
            text_content = paragraph.text.strip()
            if text_content:
                element = ExtractedElement(
                    element_type="text",
                    content=text_content,
                    table_index=table_index,
                    row_index=row_index,
                    col_index=col_index,
                    order_index=self.order_counter,
                    additional_info={
                        "paragraph_style": paragraph.style.name if paragraph.style else None,
                        "runs_count": len(paragraph.runs)
                    }
                )
                self.extracted_elements.append(element)
                self.order_counter += 1
    
    def _extract_images_from_cell_simple(self, cell: _Cell, table_index: int, row_index: int, col_index: int):
        """改进的图片提取方法 - 只检测真实图片"""
        try:
            # 检查段落中的运行是否包含图片
            for paragraph in cell.paragraphs:
                for run in paragraph.runs:
                    # 检查运行的XML中是否包含图片元素
                    run_xml = run._element.xml

                    # 更严格的检查：只有当XML中包含embed属性时才认为是图片
                    if 'r:embed=' in run_xml and ('drawing' in run_xml or 'pic:pic' in run_xml):
                        # 进一步验证是否真的有embed关系
                        import re
                        embed_matches = re.findall(r'r:embed="([^"]+)"', run_xml)
                        if embed_matches:
                            # 发现真实图片
                            element = ExtractedElement(
                                element_type="image",
                                content=f"图片_表格{table_index}_行{row_index}_列{col_index}_序号{self.order_counter}",
                                table_index=table_index,
                                row_index=row_index,
                                col_index=col_index,
                                order_index=self.order_counter,
                                additional_info={
                                    "detected_in_run": True,
                                    "embed_id": embed_matches[0],
                                    "xml_contains": "drawing" if "drawing" in run_xml else "pic"
                                }
                            )
                            self.extracted_elements.append(element)
                            self.order_counter += 1
                            print(f"    发现真实图片元素 (embed: {embed_matches[0]})")

        except Exception as e:
            print(f"    检查图片时出错: {e}")
    
    def _check_nested_tables(self, cell: _Cell, table_index: int, row_index: int, col_index: int):
        """检查嵌套表格（简化版本）"""
        try:
            # 检查单元格的XML中是否包含表格元素
            cell_xml = cell._tc.xml
            if '<w:tbl' in cell_xml:
                # 发现嵌套表格
                element = ExtractedElement(
                    element_type="nested_table",
                    content=f"嵌套表格_表格{table_index}_行{row_index}_列{col_index}",
                    table_index=table_index,
                    row_index=row_index,
                    col_index=col_index,
                    order_index=self.order_counter,
                    additional_info={
                        "detected_in_xml": True,
                        "xml_length": len(cell_xml)
                    }
                )
                self.extracted_elements.append(element)
                self.order_counter += 1
                print(f"    发现嵌套表格")
                
        except Exception as e:
            print(f"    检查嵌套表格时出错: {e}")
    
    def _extract_images_from_docx(self):
        """从docx文件中提取所有图片"""
        try:
            # 打开docx文件作为zip文件
            with zipfile.ZipFile(self.docx_path, 'r') as docx_zip:
                # 查找媒体文件
                media_files = [f for f in docx_zip.namelist() if f.startswith('word/media/')]
                
                print(f"    发现 {len(media_files)} 个媒体文件")
                
                for i, media_file in enumerate(media_files):
                    # 提取文件扩展名
                    file_ext = os.path.splitext(media_file)[1]
                    
                    # 生成新的文件名
                    new_filename = f"extracted_image_{i}{file_ext}"
                    output_path = os.path.join(self.output_dir, "images", new_filename)
                    
                    # 提取并保存图片
                    with docx_zip.open(media_file) as source, open(output_path, 'wb') as target:
                        shutil.copyfileobj(source, target)
                    
                    # 记录图片信息
                    element = ExtractedElement(
                        element_type="image",
                        content=new_filename,
                        table_index=-1,  # 表示全局图片
                        row_index=-1,
                        col_index=-1,
                        order_index=self.order_counter,
                        additional_info={
                            "original_path": media_file,
                            "extracted_path": output_path,
                            "file_size": len(docx_zip.read(media_file))
                        }
                    )
                    self.extracted_elements.append(element)
                    self.order_counter += 1
                    
        except Exception as e:
            print(f"    提取图片时出错: {e}")
    
    def save_results(self):
        """保存提取结果"""
        # 先提取所有图片
        print("\n提取文档中的所有图片...")
        self._extract_images_from_docx()
        
        # 保存为JSON格式
        results = {
            "document_path": self.docx_path,
            "total_elements": len(self.extracted_elements),
            "extraction_summary": self._get_extraction_summary(),
            "elements": [asdict(element) for element in self.extracted_elements]
        }
        
        json_path = os.path.join(self.output_dir, "extraction_results.json")
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        # 生成可读的报告
        self._generate_readable_report()
        
        print(f"\n结果已保存到: {self.output_dir}")
        print(f"- JSON结果: {json_path}")
        print(f"- 可读报告: {os.path.join(self.output_dir, 'extraction_report.txt')}")
        print(f"- 图片目录: {os.path.join(self.output_dir, 'images')}")
    
    def _get_extraction_summary(self) -> Dict[str, int]:
        """获取提取摘要"""
        summary = {}
        for element in self.extracted_elements:
            element_type = element.element_type
            summary[element_type] = summary.get(element_type, 0) + 1
        return summary
    
    def _generate_readable_report(self):
        """生成可读的提取报告"""
        report_path = os.path.join(self.output_dir, "extraction_report.txt")
        
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("DocX表格内容提取报告\n")
            f.write("=" * 50 + "\n\n")
            
            f.write(f"文档路径: {self.docx_path}\n")
            f.write(f"提取时间: {self._get_current_time()}\n")
            f.write(f"总元素数: {len(self.extracted_elements)}\n\n")
            
            # 提取摘要
            summary = self._get_extraction_summary()
            f.write("提取摘要:\n")
            for element_type, count in summary.items():
                f.write(f"  {element_type}: {count} 个\n")
            f.write("\n")
            
            # 按顺序列出所有元素
            f.write("详细内容 (按提取顺序):\n")
            f.write("-" * 30 + "\n")
            
            for element in self.extracted_elements:
                f.write(f"\n[{element.order_index}] {element.element_type.upper()}\n")
                if element.table_index >= 0:
                    f.write(f"位置: 表格{element.table_index} -> 行{element.row_index} -> 列{element.col_index}\n")
                else:
                    f.write(f"位置: 全局元素\n")
                f.write(f"内容: {element.content[:100]}{'...' if len(element.content) > 100 else ''}\n")
                
                if element.additional_info:
                    f.write(f"附加信息: {element.additional_info}\n")
    
    def _get_current_time(self) -> str:
        """获取当前时间字符串"""
        from datetime import datetime
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")


def main():
    """主函数"""
    # 使用示例
    docx_path = "input.docx"  # 输入的docx文件路径
    output_dir = "extracted_content"  # 输出目录
    
    if not os.path.exists(docx_path):
        print(f"错误: 找不到文件 {docx_path}")
        return
    
    try:
        # 创建提取器
        extractor = SimpleDocxExtractor(docx_path, output_dir)
        
        # 提取内容
        extractor.extract_all_content()
        
        # 保存结果
        extractor.save_results()
        
        print("\n提取完成！")
        
    except Exception as e:
        print(f"提取过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
