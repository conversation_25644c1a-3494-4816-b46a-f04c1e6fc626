# DocX表格内容提取器

这个Python程序可以提取docx文档中表格单元格的所有内容，包括文本、图片、文本框和嵌套表格，并建立前后顺序关系。

## 功能特性

- ✅ 提取表格中的文本内容
- ✅ 提取并保存图片文件
- ✅ 提取文本框内容
- ✅ 提取嵌套表格结构
- ✅ 建立元素的前后顺序关系
- ✅ 生成详细的提取报告
- ✅ 支持JSON格式输出

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

### 基本使用

```python
from docx_table_extractor import DocxTableExtractor

# 创建提取器
extractor = DocxTableExtractor("input.docx", "extracted_content")

# 提取内容
extractor.extract_all_content()

# 保存结果
extractor.save_results()
```

### 命令行使用

```bash
python docx_table_extractor.py
```

## 输出结构

程序会在指定的输出目录中生成以下文件：

```
extracted_content/
├── extraction_results.json    # JSON格式的详细结果
├── extraction_report.txt      # 可读的提取报告
└── images/                    # 提取的图片文件
    ├── table_0_row_0_col_0_img_1.jpg
    ├── table_0_row_0_col_1_img_3.png
    └── ...
```

## 数据结构

每个提取的元素包含以下信息：

```json
{
  "element_type": "text|image|textbox|nested_table",
  "content": "元素内容",
  "table_index": 0,
  "row_index": 0,
  "col_index": 0,
  "order_index": 0,
  "additional_info": {
    // 额外信息，根据元素类型而定
  }
}
```

## 元素类型说明

### 1. 文本 (text)
- `content`: 文本内容
- `additional_info`: 包含段落样式、运行数量等信息

### 2. 图片 (image)
- `content`: 图片文件名
- `additional_info`: 包含文件路径、内容类型、文件大小等信息

### 3. 文本框 (textbox)
- `content`: 文本框中的文本内容
- `additional_info`: 包含段落数量等信息

### 4. 嵌套表格 (nested_table)
- `content`: 表格描述
- `additional_info`: 包含行数、单元格数据等详细信息

## 顺序关系

所有提取的元素都有一个 `order_index` 字段，表示它们在文档中出现的顺序。这个索引是全局递增的，确保可以重建元素的原始顺序。

## 注意事项

1. 确保输入的docx文件格式正确且可读
2. 程序会自动创建输出目录
3. 图片文件会以特定格式命名，包含位置信息
4. 对于复杂的文档结构，可能需要调整XML解析逻辑

## 错误处理

程序包含完善的错误处理机制：
- 文件不存在时会给出明确提示
- XML解析错误会被捕获并记录
- 图片提取失败不会影响其他内容的提取

## 扩展功能

如需添加更多功能，可以：
1. 继承 `DocxTableExtractor` 类
2. 重写或添加新的提取方法
3. 修改输出格式

## 示例输出

```
成功加载文档: input.docx
开始提取文档中的表格内容...
文档中共有 2 个表格

处理第 1 个表格...
  处理单元格 [0, 0]...
  处理单元格 [0, 1]...
  处理单元格 [1, 0]...
  处理单元格 [1, 1]...

处理第 2 个表格...
  处理单元格 [0, 0]...
  处理单元格 [0, 1]...

提取完成！共提取 15 个元素

结果已保存到: extracted_content
- JSON结果: extracted_content/extraction_results.json
- 可读报告: extracted_content/extraction_report.txt
- 图片目录: extracted_content/images

提取完成！
```
