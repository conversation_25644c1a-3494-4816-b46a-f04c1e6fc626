# DocX表格内容提取器

这个Python程序可以提取docx文档中表格单元格的所有内容，包括文本、图片、文本框和嵌套表格，并建立前后顺序关系。

## 功能特性

- ✅ 提取表格中的文本内容
- ✅ 提取并保存图片文件（支持PNG、JPG、EMF、WMF等多种格式）
- ✅ 提取文本框内容
- ✅ 提取嵌套表格结构
- ✅ 建立元素的前后顺序关系
- ✅ 生成详细的提取报告
- ✅ 支持JSON格式输出
- ✅ 智能图片格式识别和统计
- ✅ 支持EMF和WMF矢量图格式

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

### 基本使用

```python
from docx_table_extractor import DocxTableExtractor

# 创建提取器
extractor = DocxTableExtractor("input.docx", "extracted_content")

# 提取内容
extractor.extract_all_content()

# 保存结果
extractor.save_results()
```

### 命令行使用

```bash
python docx_table_extractor.py
```

## 输出结构

程序会在指定的输出目录中生成以下文件：

```
extracted_content/
├── extraction_results.json    # JSON格式的详细结果
├── extraction_report.txt      # 可读的提取报告
└── images/                    # 提取的图片文件
    ├── table_0_row_0_col_0_img_1.jpg
    ├── table_0_row_0_col_1_img_3.png
    └── ...
```

## 数据结构

每个提取的元素包含以下信息：

```json
{
  "element_type": "text|image|textbox|nested_table",
  "content": "元素内容",
  "table_index": 0,
  "row_index": 0,
  "col_index": 0,
  "order_index": 0,
  "additional_info": {
    // 额外信息，根据元素类型而定
  }
}
```

## 元素类型说明

### 1. 文本 (text)
- `content`: 文本内容
- `additional_info`: 包含段落样式、运行数量等信息

### 2. 图片 (image)
- `content`: 图片文件名
- `additional_info`: 包含文件路径、内容类型、文件大小、格式名称等信息
- 支持的格式：PNG、JPG/JPEG、GIF、BMP、TIFF、WebP、EMF、WMF、SVG等

### 3. 文本框 (textbox)
- `content`: 文本框中的文本内容
- `additional_info`: 包含段落数量等信息

### 4. 嵌套表格 (nested_table)
- `content`: 表格描述
- `additional_info`: 包含行数、单元格数据等详细信息

## 顺序关系

所有提取的元素都有一个 `order_index` 字段，表示它们在文档中出现的顺序。这个索引是全局递增的，确保可以重建元素的原始顺序。

## 图片格式支持

程序支持提取多种图片格式，包括：

### 常见位图格式
- **PNG**: 便携式网络图形格式
- **JPG/JPEG**: 联合图像专家组格式
- **GIF**: 图形交换格式
- **BMP**: 位图格式
- **TIFF/TIF**: 标记图像文件格式
- **WebP**: 现代Web图像格式

### 矢量图格式
- **EMF**: 增强型图元文件（Enhanced Metafile）
- **WMF**: Windows图元文件（Windows Metafile）
- **SVG**: 可缩放矢量图形

### 提取方式
- **完整版提取器**: 通过关系ID精确提取表格和段落中引用的图片
- **简化版提取器**: 从docx文件的媒体目录中提取所有图片文件，包括未被直接引用的图片

### 格式识别
程序会自动识别图片的content_type并设置正确的文件扩展名，同时提供详细的格式统计信息。

## 注意事项

1. 确保输入的docx文件格式正确且可读
2. 程序会自动创建输出目录
3. 图片文件会以特定格式命名，包含位置信息
4. 对于复杂的文档结构，可能需要调整XML解析逻辑

## 错误处理

程序包含完善的错误处理机制：
- 文件不存在时会给出明确提示
- XML解析错误会被捕获并记录
- 图片提取失败不会影响其他内容的提取

## 扩展功能

如需添加更多功能，可以：
1. 继承 `DocxTableExtractor` 类
2. 重写或添加新的提取方法
3. 修改输出格式

## 示例输出

### 完整版提取器输出
```
成功加载文档: input.docx
开始提取文档中的所有内容...
文档中共有 9 个表格
文档中共有 35 个段落

处理第 1 个表格...
  处理单元格 [0, 0]...
  处理单元格 [0, 1]...
  ...

处理第 6 个表格...
  处理单元格 [1, 1]...
    提取图片: table_5_row_1_col_1_img_954.png (格式: image/png, 大小: 117167 bytes)
    提取图片: table_5_row_1_col_1_img_955.png (格式: image/png, 大小: 18351 bytes)
    ...

提取完成！共提取 1681 个元素
```

### 简化版提取器输出
```
提取文档中的所有图片...
    发现 14 个媒体文件
      提取: extracted_image_2.emf (EMF, 28488 bytes)
      提取: extracted_image_3.png (PNG, 25313 bytes)
      提取: extracted_image_5.wmf (WMF, 1466 bytes)
      提取: extracted_image_6.wmf (WMF, 924 bytes)
      ...
    图片格式统计:
      EMF: 4 个
      PNG: 7 个
      WMF: 2 个

提取完成！共提取 1600 个元素

结果已保存到: extracted_content
- JSON结果: extracted_content/extraction_results.json
- 可读报告: extracted_content/extraction_report.txt
- 图片目录: extracted_content/images
```
