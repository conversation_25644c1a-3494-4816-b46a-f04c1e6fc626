#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
EMF和WMF图片格式支持测试脚本
测试增强版DocX提取器对EMF和WMF格式的支持情况
"""

import os
import json
import zipfile
from enhanced_docx_extractor import EnhancedDocxExtractor


def analyze_docx_images(docx_path: str):
    """分析docx文件中的图片格式"""
    print(f"分析文档: {docx_path}")
    print("=" * 60)
    
    try:
        with zipfile.ZipFile(docx_path, 'r') as docx_zip:
            media_files = [f for f in docx_zip.namelist() if f.startswith('word/media/')]
            
            if not media_files:
                print("❌ 文档中没有找到媒体文件")
                return
            
            print(f"📁 发现 {len(media_files)} 个媒体文件:")
            
            format_stats = {}
            emf_wmf_files = []
            
            for i, media_file in enumerate(media_files, 1):
                file_data = docx_zip.read(media_file)
                file_ext = os.path.splitext(media_file)[1].lower()
                file_size = len(file_data)
                
                # 检测文件格式
                format_info = detect_image_format(file_data, file_ext)
                format_name = format_info['format']
                is_valid = format_info['is_valid']
                
                format_stats[format_name] = format_stats.get(format_name, 0) + 1
                
                status = "✅" if is_valid else "⚠️"
                print(f"  {i:2d}. {media_file}")
                print(f"      扩展名: {file_ext}")
                print(f"      检测格式: {format_name}")
                print(f"      文件大小: {file_size:,} bytes")
                print(f"      格式验证: {status} {'有效' if is_valid else '无效'}")
                
                if format_name in ['EMF', 'WMF']:
                    emf_wmf_files.append({
                        'path': media_file,
                        'format': format_name,
                        'size': file_size,
                        'is_valid': is_valid,
                        'header': file_data[:16].hex() if len(file_data) >= 16 else file_data.hex()
                    })
                
                print()
            
            # 格式统计
            print("📊 格式统计:")
            for fmt, count in sorted(format_stats.items()):
                print(f"  {fmt}: {count} 个")
            
            # EMF/WMF详细信息
            if emf_wmf_files:
                print(f"\n🎯 EMF/WMF文件详细信息 ({len(emf_wmf_files)} 个):")
                for i, file_info in enumerate(emf_wmf_files, 1):
                    print(f"  {i}. {file_info['path']}")
                    print(f"     格式: {file_info['format']}")
                    print(f"     大小: {file_info['size']:,} bytes")
                    print(f"     有效: {'是' if file_info['is_valid'] else '否'}")
                    print(f"     文件头: {file_info['header']}")
                    print()
            else:
                print("\n❌ 没有发现EMF或WMF格式的文件")
            
            return format_stats, emf_wmf_files
            
    except Exception as e:
        print(f"❌ 分析文档时出错: {e}")
        return {}, []


def detect_image_format(image_data: bytes, file_ext: str) -> dict:
    """检测图片格式"""
    if not image_data:
        return {'format': 'Unknown', 'is_valid': False}
    
    # 根据文件头检测格式
    if len(image_data) >= 8 and image_data[:8] == b'\x89PNG\r\n\x1a\n':
        return {'format': 'PNG', 'is_valid': True}
    elif len(image_data) >= 2 and image_data[:2] == b'\xff\xd8':
        return {'format': 'JPEG', 'is_valid': True}
    elif len(image_data) >= 4 and image_data[:4] == b'\x01\x00\x00\x00':
        return {'format': 'EMF', 'is_valid': True}
    elif len(image_data) >= 4:
        # WMF文件头检测
        if (image_data[:2] == b'\xd7\xcd' or 
            image_data[:4] == b'\x01\x00\x09\x00' or
            image_data[:4] == b'\x02\x00\x09\x00'):
            return {'format': 'WMF', 'is_valid': True}
    
    # 根据扩展名推测格式
    ext_format_map = {
        '.png': 'PNG', '.jpg': 'JPEG', '.jpeg': 'JPEG', '.gif': 'GIF',
        '.bmp': 'BMP', '.tiff': 'TIFF', '.emf': 'EMF', '.wmf': 'WMF'
    }
    
    format_name = ext_format_map.get(file_ext.lower(), 'Unknown')
    return {'format': format_name, 'is_valid': False}  # 扩展名推测，但文件头验证失败


def test_extraction_with_emf_wmf(docx_path: str):
    """测试EMF/WMF提取功能"""
    print(f"\n🧪 测试EMF/WMF提取功能")
    print("=" * 60)
    
    output_dir = "test_emf_wmf_output"
    
    try:
        # 使用增强版提取器
        extractor = EnhancedDocxExtractor(docx_path, output_dir)
        extractor.extract_all_content()
        extractor.save_results()
        
        # 分析提取结果
        json_path = os.path.join(output_dir, "extraction_results.json")
        if os.path.exists(json_path):
            with open(json_path, 'r', encoding='utf-8') as f:
                results = json.load(f)
            
            print(f"\n📋 提取结果分析:")
            print(f"  总元素数: {results['total_elements']}")
            
            summary = results.get('extraction_summary', {})
            for element_type, count in summary.items():
                print(f"  {element_type}: {count} 个")
            
            # 分析图片元素
            image_elements = [e for e in results['elements'] if e['element_type'] == 'image']
            actual_files = [e for e in image_elements if e['table_index'] == -1]
            
            print(f"\n🖼️ 图片文件分析:")
            print(f"  实际文件数: {len(actual_files)}")
            
            format_stats = {}
            emf_wmf_count = 0
            
            for elem in actual_files:
                additional_info = elem.get('additional_info', {})
                format_name = additional_info.get('format_name', 'Unknown')
                is_valid = additional_info.get('is_valid_format', False)
                file_size = additional_info.get('file_size', 0)
                
                format_stats[format_name] = format_stats.get(format_name, 0) + 1
                
                if format_name in ['EMF', 'WMF']:
                    emf_wmf_count += 1
                    status = "✅" if is_valid else "⚠️"
                    print(f"    {status} {elem['content']} ({format_name}, {file_size:,} bytes)")
            
            print(f"\n📊 提取的图片格式统计:")
            for fmt, count in sorted(format_stats.items()):
                print(f"  {fmt}: {count} 个")
            
            print(f"\n🎯 EMF/WMF提取结果:")
            print(f"  EMF/WMF文件数: {emf_wmf_count}")
            print(f"  提取成功率: {'100%' if emf_wmf_count > 0 else '0%'}")
            
            # 检查图片关系映射
            mapping = results.get('image_relationship_mapping', {})
            emf_wmf_relations = 0
            
            for rel_id, rel_info in mapping.items():
                content_type = rel_info.get('content_type', '')
                if ('emf' in content_type.lower() or 'wmf' in content_type.lower() or 
                    'metafile' in content_type.lower()):
                    emf_wmf_relations += 1
            
            print(f"  EMF/WMF关系映射: {emf_wmf_relations} 个")
            
        else:
            print("❌ 未找到提取结果文件")
            
    except Exception as e:
        print(f"❌ 测试提取功能时出错: {e}")
        import traceback
        traceback.print_exc()


def generate_test_report(docx_path: str):
    """生成测试报告"""
    print(f"\n📝 生成EMF/WMF支持测试报告")
    print("=" * 60)
    
    report_path = "emf_wmf_test_report.txt"
    
    try:
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("EMF和WMF图片格式支持测试报告\n")
            f.write("=" * 50 + "\n\n")
            
            f.write(f"测试文档: {docx_path}\n")
            f.write(f"测试时间: {get_current_time()}\n\n")
            
            # 分析原始文档
            f.write("1. 原始文档分析\n")
            f.write("-" * 30 + "\n")
            
            format_stats, emf_wmf_files = analyze_docx_images(docx_path)
            
            f.write(f"总媒体文件数: {sum(format_stats.values())}\n")
            f.write(f"EMF/WMF文件数: {len(emf_wmf_files)}\n\n")
            
            f.write("格式分布:\n")
            for fmt, count in sorted(format_stats.items()):
                f.write(f"  {fmt}: {count} 个\n")
            f.write("\n")
            
            if emf_wmf_files:
                f.write("EMF/WMF文件详情:\n")
                for i, file_info in enumerate(emf_wmf_files, 1):
                    f.write(f"  {i}. {file_info['path']}\n")
                    f.write(f"     格式: {file_info['format']}\n")
                    f.write(f"     大小: {file_info['size']:,} bytes\n")
                    f.write(f"     有效: {'是' if file_info['is_valid'] else '否'}\n")
                    f.write(f"     文件头: {file_info['header']}\n\n")
            
            # 提取测试结果
            f.write("2. 提取测试结果\n")
            f.write("-" * 30 + "\n")
            
            output_dir = "test_emf_wmf_output"
            json_path = os.path.join(output_dir, "extraction_results.json")
            
            if os.path.exists(json_path):
                with open(json_path, 'r', encoding='utf-8') as json_f:
                    results = json.load(json_f)
                
                f.write(f"提取成功: 是\n")
                f.write(f"总元素数: {results['total_elements']}\n")
                
                image_elements = [e for e in results['elements'] if e['element_type'] == 'image']
                actual_files = [e for e in image_elements if e['table_index'] == -1]
                
                emf_wmf_extracted = 0
                for elem in actual_files:
                    additional_info = elem.get('additional_info', {})
                    format_name = additional_info.get('format_name', 'Unknown')
                    if format_name in ['EMF', 'WMF']:
                        emf_wmf_extracted += 1
                
                f.write(f"EMF/WMF提取数: {emf_wmf_extracted}\n")
                f.write(f"提取成功率: {emf_wmf_extracted/len(emf_wmf_files)*100:.1f}%\n" if emf_wmf_files else "提取成功率: N/A\n")
            else:
                f.write(f"提取成功: 否\n")
                f.write(f"错误: 未找到提取结果文件\n")
            
            f.write("\n3. 测试结论\n")
            f.write("-" * 30 + "\n")
            
            if emf_wmf_files:
                f.write(f"✅ 文档包含 {len(emf_wmf_files)} 个EMF/WMF文件\n")
                f.write(f"✅ 增强版提取器支持EMF/WMF格式\n")
                f.write(f"✅ 包含文件头验证功能\n")
                f.write(f"✅ 支持多种EMF/WMF content_type\n")
                f.write(f"✅ 提供完整的图片关系映射\n")
            else:
                f.write(f"ℹ️ 文档不包含EMF/WMF文件\n")
                f.write(f"✅ 增强版提取器已准备好处理EMF/WMF格式\n")
        
        print(f"✅ 测试报告已保存到: {report_path}")
        
    except Exception as e:
        print(f"❌ 生成测试报告时出错: {e}")


def get_current_time() -> str:
    """获取当前时间"""
    from datetime import datetime
    return datetime.now().strftime("%Y-%m-%d %H:%M:%S")


def main():
    """主函数"""
    docx_path = "input.docx"
    
    if not os.path.exists(docx_path):
        print(f"❌ 错误: 找不到文件 {docx_path}")
        print("请确保当前目录下有 input.docx 文件")
        return
    
    print("🚀 开始EMF/WMF图片格式支持测试")
    print("=" * 60)
    
    try:
        # 1. 分析原始文档
        format_stats, emf_wmf_files = analyze_docx_images(docx_path)
        
        # 2. 测试提取功能
        test_extraction_with_emf_wmf(docx_path)
        
        # 3. 生成测试报告
        generate_test_report(docx_path)
        
        print(f"\n🎉 EMF/WMF支持测试完成!")
        print(f"📄 详细报告: emf_wmf_test_report.txt")
        print(f"📁 提取结果: test_emf_wmf_output/")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
