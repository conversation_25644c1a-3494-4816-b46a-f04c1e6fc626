#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试图片提取功能，特别是EMF和WMF格式的支持
"""

import os
import shutil
from docx_table_extractor import DocxTableExtractor
from simple_docx_extractor import SimpleDocxExtractor


def test_image_extraction():
    """测试图片提取功能"""
    docx_path = "input.docx"
    
    if not os.path.exists(docx_path):
        print(f"错误: 找不到文件 {docx_path}")
        return
    
    # 测试完整版提取器
    print("=" * 60)
    print("测试完整版提取器 (DocxTableExtractor)")
    print("=" * 60)
    
    output_dir_full = "test_extracted_full"
    if os.path.exists(output_dir_full):
        shutil.rmtree(output_dir_full)
    
    try:
        extractor_full = DocxTableExtractor(docx_path, output_dir_full)
        extractor_full.extract_all_content()
        extractor_full.save_results()
        
        # 分析提取的图片
        analyze_extracted_images(output_dir_full)
        
    except Exception as e:
        print(f"完整版提取器测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "=" * 60)
    print("测试简化版提取器 (SimpleDocxExtractor)")
    print("=" * 60)
    
    # 测试简化版提取器
    output_dir_simple = "test_extracted_simple"
    if os.path.exists(output_dir_simple):
        shutil.rmtree(output_dir_simple)
    
    try:
        extractor_simple = SimpleDocxExtractor(docx_path, output_dir_simple)
        extractor_simple.extract_all_content()
        extractor_simple.save_results()
        
        # 分析提取的图片
        analyze_extracted_images(output_dir_simple)
        
    except Exception as e:
        print(f"简化版提取器测试失败: {e}")
        import traceback
        traceback.print_exc()


def analyze_extracted_images(output_dir):
    """分析提取的图片文件"""
    images_dir = os.path.join(output_dir, "images")
    
    if not os.path.exists(images_dir):
        print(f"  图片目录不存在: {images_dir}")
        return
    
    image_files = [f for f in os.listdir(images_dir) if os.path.isfile(os.path.join(images_dir, f))]
    
    if not image_files:
        print(f"  未找到图片文件")
        return
    
    print(f"\n图片提取分析 ({output_dir}):")
    print(f"  总图片数量: {len(image_files)}")
    
    # 按格式分类
    format_stats = {}
    total_size = 0
    
    for filename in image_files:
        filepath = os.path.join(images_dir, filename)
        file_size = os.path.getsize(filepath)
        total_size += file_size
        
        # 获取文件扩展名
        ext = os.path.splitext(filename)[1].lower()
        format_stats[ext] = format_stats.get(ext, {'count': 0, 'size': 0})
        format_stats[ext]['count'] += 1
        format_stats[ext]['size'] += file_size
        
        print(f"    {filename}: {file_size} bytes")
    
    print(f"\n  格式统计:")
    for ext, stats in sorted(format_stats.items()):
        count = stats['count']
        size = stats['size']
        avg_size = size // count if count > 0 else 0
        print(f"    {ext.upper()}: {count} 个文件, 总大小: {size} bytes, 平均大小: {avg_size} bytes")
    
    print(f"  总大小: {total_size} bytes")
    
    # 检查EMF和WMF文件
    emf_files = [f for f in image_files if f.lower().endswith('.emf')]
    wmf_files = [f for f in image_files if f.lower().endswith('.wmf')]
    
    if emf_files:
        print(f"\n  EMF文件 ({len(emf_files)} 个):")
        for f in emf_files:
            print(f"    {f}")
    
    if wmf_files:
        print(f"\n  WMF文件 ({len(wmf_files)} 个):")
        for f in wmf_files:
            print(f"    {f}")
    
    if not emf_files and not wmf_files:
        print(f"\n  注意: 未发现EMF或WMF格式的图片文件")


def compare_extraction_results():
    """比较两种提取器的结果"""
    print("\n" + "=" * 60)
    print("比较提取结果")
    print("=" * 60)
    
    full_dir = "test_extracted_full/images"
    simple_dir = "test_extracted_simple/images"
    
    if not os.path.exists(full_dir) or not os.path.exists(simple_dir):
        print("无法比较: 缺少提取结果目录")
        return
    
    full_files = set(os.listdir(full_dir)) if os.path.exists(full_dir) else set()
    simple_files = set(os.listdir(simple_dir)) if os.path.exists(simple_dir) else set()
    
    print(f"完整版提取器: {len(full_files)} 个图片文件")
    print(f"简化版提取器: {len(simple_files)} 个图片文件")
    
    # 分析差异
    only_in_full = full_files - simple_files
    only_in_simple = simple_files - full_files
    common_files = full_files & simple_files
    
    print(f"共同文件: {len(common_files)} 个")
    
    if only_in_full:
        print(f"仅在完整版中: {len(only_in_full)} 个")
        for f in sorted(only_in_full):
            print(f"  {f}")
    
    if only_in_simple:
        print(f"仅在简化版中: {len(only_in_simple)} 个")
        for f in sorted(only_in_simple):
            print(f"  {f}")


if __name__ == "__main__":
    test_image_extraction()
    compare_extraction_results()
    print("\n测试完成！")
