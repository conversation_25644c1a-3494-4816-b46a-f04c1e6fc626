# 图片文件名与JSON/TXT输出对应关系功能总结

## 功能概述

成功实现了DocX文档中图片的完整关系映射功能，能够建立表格中检测到的图片引用（embed_id）与实际提取的图片文件之间的对应关系。

## 核心功能

### 1. 图片关系映射
- **建立embed_id到文件名的映射**: 通过解析docx文件的关系映射，将表格中的图片引用（如rId17, rId18）与实际的图片文件名（如extracted_image_10.png）建立对应关系
- **双向引用关系**: 不仅在图片引用中记录对应的文件名，还在实际文件中记录被哪些元素引用
- **完整的元数据**: 包含原始路径、文件大小、格式信息、content_type等详细信息

### 2. 支持的图片格式
- **PNG**: 标准位图格式
- **EMF**: Enhanced Metafile Format（增强型图元文件）
- **WMF**: Windows Metafile Format（Windows图元文件）
- **JPEG/JPG**: 标准压缩图片格式
- **GIF, BMP, TIFF, WebP, SVG**: 其他常见格式

### 3. JSON输出结构

#### 表格中的图片引用
```json
{
  "element_type": "image",
  "content": "图片_表格5_行1_列1_序号906",
  "table_index": 5,
  "row_index": 1,
  "col_index": 1,
  "order_index": 906,
  "additional_info": {
    "embed_id": "rId17",
    "xml_contains": "drawing",
    "actual_filename": "extracted_image_10.png",
    "original_media_path": "word/media/image5.png",
    "content_type": "image/png"
  }
}
```

#### 实际的图片文件
```json
{
  "element_type": "image",
  "content": "extracted_image_10.png",
  "table_index": -1,
  "row_index": -1,
  "col_index": -1,
  "order_index": 1609,
  "additional_info": {
    "original_path": "word/media/image5.png",
    "extracted_path": "demo_output\\images\\extracted_image_10.png",
    "file_size": 117167,
    "format_name": "PNG",
    "file_extension": ".png",
    "is_supported_format": true,
    "relationship_ids": ["rId17"],
    "referenced_by_elements": [
      {
        "order_index": 906,
        "table_index": 5,
        "row_index": 1,
        "col_index": 1,
        "embed_id": "rId17"
      }
    ]
  }
}
```

## 技术实现

### 1. 关系映射机制
- **解析文档关系**: 通过`document.part.rels`获取所有图片关系
- **建立映射表**: 将关系ID与原始媒体路径建立映射
- **文件名关联**: 将原始路径与提取后的文件名建立关联

### 2. 核心方法

#### SimpleDocxExtractor中的关键方法
```python
def _build_image_relationship_mapping(self):
    """建立图片关系ID与实际文件的映射关系"""

def _update_image_references(self, image_mapping, path_to_filename):
    """更新图片元素的引用关系"""
```

#### DocxTableExtractor中的改进
```python
def _extract_image_by_rel_id(self, rel_id, table_index, row_index, col_index, parent_element):
    """通过关系ID提取图片，包含完整的关系信息"""

def _get_format_name_from_content_type(self, content_type):
    """根据content_type获取格式名称"""
```

## 测试结果

### 测试文档统计
- **总图片元素**: 21个
- **表格中的图片引用**: 7个
- **实际的图片文件**: 14个
- **映射成功率**: 100%

### 格式分布
- **PNG**: 7个文件
- **EMF**: 4个文件
- **WMF**: 2个文件
- **Unknown**: 1个文件（0字节）

### 映射关系示例
```
表格5 行1 列1 (embed: rId17) → extracted_image_10.png
表格5 行1 列1 (embed: rId18) → extracted_image_11.png
表格5 行1 列1 (embed: rId19) → extracted_image_12.png
表格5 行1 列1 (embed: rId20) → extracted_image_13.png
表格5 行1 列1 (embed: rId21) → extracted_image_14.png
表格5 行1 列1 (embed: rId22) → extracted_image_3.png
表格5 行1 列1 (embed: rId23) → extracted_image_4.png
```

## 输出文件

### 1. JSON结果文件
- **文件名**: `extraction_results.json`
- **内容**: 包含完整的元素信息和关系映射
- **用途**: 程序化处理和数据分析

### 2. 可读报告文件
- **文件名**: `extraction_report.txt`
- **内容**: 人类可读的提取结果总结
- **用途**: 快速查看提取概况

### 3. 图片关系报告
- **文件名**: `image_relationship_report.txt`
- **内容**: 详细的图片关系映射分析
- **用途**: 理解图片引用关系

### 4. 使用示例代码
- **文件名**: `usage_examples.py`
- **内容**: 如何使用JSON数据查找图片关系的示例代码
- **用途**: 开发参考

## 使用方法

### 基本使用
```python
from simple_docx_extractor import SimpleDocxExtractor

# 创建提取器
extractor = SimpleDocxExtractor("input.docx", "output_dir")

# 提取内容（包含图片关系映射）
extractor.extract_all_content()

# 保存结果
extractor.save_results()
```

### 查找图片关系
```python
import json

# 加载结果
with open("output_dir/extraction_results.json", 'r', encoding='utf-8') as f:
    data = json.load(f)

# 查找特定位置的图片
for elem in data['elements']:
    if (elem['element_type'] == 'image' and 
        elem['table_index'] == 5 and 
        elem['row_index'] == 1 and 
        elem['col_index'] == 1):
        
        actual_filename = elem['additional_info'].get('actual_filename')
        embed_id = elem['additional_info'].get('embed_id')
        print(f"图片引用 {embed_id} 对应文件: {actual_filename}")
```

## 优势特点

1. **完整性**: 支持所有常见图片格式，包括EMF和WMF矢量格式
2. **准确性**: 100%的映射成功率，精确建立引用关系
3. **详细性**: 提供丰富的元数据信息，包括文件大小、格式、路径等
4. **易用性**: 提供多种输出格式和使用示例
5. **可扩展性**: 模块化设计，易于扩展新功能

## 应用场景

- **文档分析**: 分析Word文档中的图片使用情况
- **内容提取**: 批量提取文档中的图片资源
- **格式转换**: 将文档中的图片转换为独立文件
- **数据挖掘**: 分析文档结构和图片分布
- **自动化处理**: 程序化处理大量文档的图片内容
